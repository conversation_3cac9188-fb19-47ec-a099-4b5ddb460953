"""
Async operations optimization for Phase 3 performance improvements.

This module provides enhanced async patterns and optimizations including:
- Converting blocking operations to async
- Concurrent agent loading and initialization
- Async context managers for resource management
- Thread pool optimization for CPU-bound tasks
"""

import logging
import asyncio
import aiofiles
import aiofiles.os
from typing import Dict, Any, List, Optional, Callable, Union, Coroutine
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from contextlib import asynccontextmanager
import time
import json
import yaml
from pathlib import Path
import functools

from ..database import get_db
from ..redis_client import redis_client

logger = logging.getLogger(__name__)


class AsyncFileManager:
    """
    Async file operations manager for non-blocking I/O.
    
    Provides async alternatives to blocking file operations
    commonly used throughout the application.
    """
    
    def __init__(self, max_workers: int = 10):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.performance_metrics = {
            'files_read': 0,
            'files_written': 0,
            'total_read_time': 0.0,
            'total_write_time': 0.0,
            'errors': 0
        }
    
    async def read_file_async(self, file_path: str, encoding: str = 'utf-8') -> str:
        """Read file content asynchronously."""
        start_time = time.time()
        
        try:
            async with aiofiles.open(file_path, 'r', encoding=encoding) as file:
                content = await file.read()
            
            self.performance_metrics['files_read'] += 1
            self.performance_metrics['total_read_time'] += time.time() - start_time
            
            logger.debug(f"Async read completed: {file_path}")
            return content
            
        except Exception as e:
            self.performance_metrics['errors'] += 1
            logger.error(f"Async file read failed for {file_path}: {e}")
            raise
    
    async def write_file_async(self, file_path: str, content: str, encoding: str = 'utf-8') -> bool:
        """Write file content asynchronously."""
        start_time = time.time()
        
        try:
            async with aiofiles.open(file_path, 'w', encoding=encoding) as file:
                await file.write(content)
            
            self.performance_metrics['files_written'] += 1
            self.performance_metrics['total_write_time'] += time.time() - start_time
            
            logger.debug(f"Async write completed: {file_path}")
            return True
            
        except Exception as e:
            self.performance_metrics['errors'] += 1
            logger.error(f"Async file write failed for {file_path}: {e}")
            return False
    
    async def read_yaml_async(self, file_path: str) -> Dict[str, Any]:
        """Read YAML file asynchronously."""
        content = await self.read_file_async(file_path)
        return yaml.safe_load(content)
    
    async def write_yaml_async(self, file_path: str, data: Dict[str, Any]) -> bool:
        """Write YAML file asynchronously."""
        content = yaml.dump(data, default_flow_style=False)
        return await self.write_file_async(file_path, content)
    
    async def read_json_async(self, file_path: str) -> Dict[str, Any]:
        """Read JSON file asynchronously."""
        content = await self.read_file_async(file_path)
        return json.loads(content)
    
    async def write_json_async(self, file_path: str, data: Dict[str, Any]) -> bool:
        """Write JSON file asynchronously."""
        content = json.dumps(data, indent=2)
        return await self.write_file_async(file_path, content)
    
    async def file_exists_async(self, file_path: str) -> bool:
        """Check if file exists asynchronously."""
        try:
            return await aiofiles.os.path.exists(file_path)
        except Exception as e:
            logger.error(f"Error checking file existence {file_path}: {e}")
            return False
    
    async def list_files_async(self, directory: str, pattern: str = "*") -> List[str]:
        """List files in directory asynchronously."""
        try:
            path = Path(directory)
            if await aiofiles.os.path.exists(directory):
                # Use thread pool for directory listing
                loop = asyncio.get_event_loop()
                files = await loop.run_in_executor(
                    self.executor,
                    lambda: list(path.glob(pattern))
                )
                return [str(f) for f in files]
            return []
        except Exception as e:
            logger.error(f"Error listing files in {directory}: {e}")
            return []
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get file operation performance metrics."""
        metrics = self.performance_metrics.copy()
        
        if metrics['files_read'] > 0:
            metrics['avg_read_time'] = metrics['total_read_time'] / metrics['files_read']
        else:
            metrics['avg_read_time'] = 0.0
        
        if metrics['files_written'] > 0:
            metrics['avg_write_time'] = metrics['total_write_time'] / metrics['files_written']
        else:
            metrics['avg_write_time'] = 0.0
        
        return metrics


class ConcurrentAgentLoader:
    """
    Concurrent agent loading system for improved startup performance.
    
    Loads multiple agents concurrently instead of sequentially
    to reduce application startup time.
    """
    
    def __init__(self, max_concurrent: int = 5):
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.loading_metrics = {
            'agents_loaded': 0,
            'loading_errors': 0,
            'total_loading_time': 0.0,
            'concurrent_loads': 0
        }
    
    async def load_agents_concurrent(self, agent_configs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Load multiple agents concurrently."""
        start_time = time.time()
        results = {
            'loaded_agents': [],
            'failed_agents': [],
            'errors': []
        }
        
        try:
            # Create loading tasks
            tasks = [
                self._load_single_agent(config)
                for config in agent_configs
            ]
            
            # Execute tasks concurrently
            self.loading_metrics['concurrent_loads'] += 1
            agent_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for i, result in enumerate(agent_results):
                agent_config = agent_configs[i]
                agent_id = agent_config.get('id', f'agent_{i}')
                
                if isinstance(result, Exception):
                    results['failed_agents'].append(agent_id)
                    results['errors'].append(f"{agent_id}: {str(result)}")
                    self.loading_metrics['loading_errors'] += 1
                else:
                    results['loaded_agents'].append(agent_id)
                    self.loading_metrics['agents_loaded'] += 1
            
            loading_time = time.time() - start_time
            self.loading_metrics['total_loading_time'] += loading_time
            
            logger.info(f"Concurrent agent loading completed: "
                       f"{len(results['loaded_agents'])} loaded, "
                       f"{len(results['failed_agents'])} failed in {loading_time:.2f}s")
            
        except Exception as e:
            logger.error(f"Concurrent agent loading failed: {e}")
            results['errors'].append(str(e))
        
        return results
    
    async def _load_single_agent(self, agent_config: Dict[str, Any]) -> Dict[str, Any]:
        """Load a single agent with concurrency control."""
        async with self.semaphore:
            agent_id = agent_config.get('id', 'unknown')
            
            try:
                # Simulate agent loading (replace with actual loading logic)
                await asyncio.sleep(0.1)  # Simulate I/O delay
                
                # Load agent configuration files if needed
                if 'config_file' in agent_config:
                    config_content = await async_file_manager.read_yaml_async(
                        agent_config['config_file']
                    )
                    agent_config.update(config_content)
                
                logger.debug(f"Agent loaded successfully: {agent_id}")
                return {'agent_id': agent_id, 'status': 'loaded'}
                
            except Exception as e:
                logger.error(f"Failed to load agent {agent_id}: {e}")
                raise
    
    def get_loading_metrics(self) -> Dict[str, Any]:
        """Get agent loading performance metrics."""
        metrics = self.loading_metrics.copy()
        
        if metrics['concurrent_loads'] > 0:
            metrics['avg_loading_time'] = metrics['total_loading_time'] / metrics['concurrent_loads']
        else:
            metrics['avg_loading_time'] = 0.0
        
        return metrics


class AsyncContextManager:
    """
    Enhanced async context managers for resource management.
    
    Provides async context managers for database sessions,
    file operations, and other resources.
    """
    
    @staticmethod
    @asynccontextmanager
    async def async_database_session():
        """Async context manager for database sessions."""
        db = next(get_db())
        try:
            yield db
        except Exception as e:
            db.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            db.close()
    
    @staticmethod
    @asynccontextmanager
    async def async_redis_connection():
        """Async context manager for Redis connections."""
        if not redis_client:
            yield None
            return
        
        try:
            yield redis_client
        except Exception as e:
            logger.error(f"Redis connection error: {e}")
            raise
    
    @staticmethod
    @asynccontextmanager
    async def async_file_processing(file_path: str, operation: str = "processing"):
        """Async context manager for file processing operations."""
        start_time = time.time()
        
        try:
            logger.debug(f"Starting {operation} for {file_path}")
            yield file_path
        except Exception as e:
            logger.error(f"File {operation} failed for {file_path}: {e}")
            raise
        finally:
            processing_time = time.time() - start_time
            logger.debug(f"Completed {operation} for {file_path} in {processing_time:.2f}s")
    
    @staticmethod
    @asynccontextmanager
    async def async_performance_monitor(operation_name: str):
        """Async context manager for performance monitoring."""
        start_time = time.time()
        
        try:
            yield operation_name
        finally:
            execution_time = time.time() - start_time
            logger.debug(f"Operation '{operation_name}' completed in {execution_time:.2f}s")


class AsyncTaskManager:
    """
    Manager for async task execution and monitoring.
    
    Provides utilities for managing background tasks,
    concurrent operations, and task lifecycle.
    """
    
    def __init__(self):
        self.active_tasks: Dict[str, asyncio.Task] = {}
        self.task_metrics = {
            'tasks_created': 0,
            'tasks_completed': 0,
            'tasks_failed': 0,
            'tasks_cancelled': 0
        }
    
    async def create_background_task(self, coro: Coroutine, task_name: str) -> str:
        """Create and track a background task."""
        task_id = f"{task_name}_{int(time.time())}"
        
        try:
            task = asyncio.create_task(coro, name=task_name)
            self.active_tasks[task_id] = task
            self.task_metrics['tasks_created'] += 1
            
            # Add completion callback
            task.add_done_callback(
                lambda t: self._task_completed(task_id, t)
            )
            
            logger.debug(f"Background task created: {task_id}")
            return task_id
            
        except Exception as e:
            logger.error(f"Failed to create background task {task_name}: {e}")
            raise
    
    def _task_completed(self, task_id: str, task: asyncio.Task):
        """Handle task completion."""
        if task_id in self.active_tasks:
            del self.active_tasks[task_id]
        
        if task.cancelled():
            self.task_metrics['tasks_cancelled'] += 1
            logger.debug(f"Task cancelled: {task_id}")
        elif task.exception():
            self.task_metrics['tasks_failed'] += 1
            logger.error(f"Task failed: {task_id}, error: {task.exception()}")
        else:
            self.task_metrics['tasks_completed'] += 1
            logger.debug(f"Task completed: {task_id}")
    
    async def cancel_task(self, task_id: str) -> bool:
        """Cancel a specific task."""
        if task_id in self.active_tasks:
            task = self.active_tasks[task_id]
            task.cancel()
            
            try:
                await task
            except asyncio.CancelledError:
                pass
            
            return True
        return False
    
    async def cancel_all_tasks(self):
        """Cancel all active tasks."""
        tasks_to_cancel = list(self.active_tasks.values())
        
        for task in tasks_to_cancel:
            task.cancel()
        
        # Wait for all tasks to complete cancellation
        if tasks_to_cancel:
            await asyncio.gather(*tasks_to_cancel, return_exceptions=True)
        
        self.active_tasks.clear()
        logger.info(f"Cancelled {len(tasks_to_cancel)} active tasks")
    
    def get_task_status(self) -> Dict[str, Any]:
        """Get current task status and metrics."""
        return {
            'active_tasks': len(self.active_tasks),
            'task_names': [task.get_name() for task in self.active_tasks.values()],
            'metrics': self.task_metrics.copy()
        }


def async_retry(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """Decorator for async functions with retry logic."""
    def decorator(func: Callable):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if attempt < max_retries:
                        wait_time = delay * (backoff ** attempt)
                        logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {e}. "
                                     f"Retrying in {wait_time:.2f}s")
                        await asyncio.sleep(wait_time)
                    else:
                        logger.error(f"All {max_retries + 1} attempts failed for {func.__name__}")
            
            raise last_exception
        
        return wrapper
    return decorator


def run_in_thread_pool(executor: Optional[ThreadPoolExecutor] = None):
    """Decorator to run sync functions in thread pool."""
    def decorator(func: Callable):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(
                executor,
                lambda: func(*args, **kwargs)
            )
        return wrapper
    return decorator


# Global instances
async_file_manager = AsyncFileManager()
concurrent_agent_loader = ConcurrentAgentLoader()
async_task_manager = AsyncTaskManager()


async def get_async_performance_metrics() -> Dict[str, Any]:
    """Get comprehensive async performance metrics."""
    return {
        'file_operations': async_file_manager.get_performance_metrics(),
        'agent_loading': concurrent_agent_loader.get_loading_metrics(),
        'task_management': async_task_manager.get_task_status()
    }
