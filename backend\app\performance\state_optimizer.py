"""
State management optimization for Phase 3 performance improvements.

This module provides enhanced state management with:
- State compression and serialization optimization
- Multi-level state caching (memory + Redis)
- Incremental state updates and delta compression
- State validation and cleanup
- Performance monitoring and metrics
"""

import logging
import json
import pickle
import gzip
import zlib
from typing import Dict, Any, List, Optional, Union, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import hashlib
import time
from contextlib import contextmanager

from ..redis_client import redis_client
from ..database import get_db

logger = logging.getLogger(__name__)


@dataclass
class StateMetrics:
    """Metrics for state management operations."""
    compressions: int = 0
    decompressions: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    serializations: int = 0
    deserializations: int = 0
    total_compression_time: float = 0.0
    total_decompression_time: float = 0.0
    total_serialization_time: float = 0.0
    total_deserialization_time: float = 0.0
    compression_ratio: float = 0.0
    cache_hit_rate: float = 0.0


class StateCompressor:
    """
    Advanced state compression system for memory and storage optimization.
    
    Provides multiple compression algorithms and automatic selection
    based on state size and content characteristics.
    """
    
    def __init__(self):
        self.metrics = StateMetrics()
        self.compression_algorithms = {
            'gzip': self._gzip_compress,
            'zlib': self._zlib_compress,
            'pickle': self._pickle_compress
        }
        self.decompression_algorithms = {
            'gzip': self._gzip_decompress,
            'zlib': self._zlib_decompress,
            'pickle': self._pickle_decompress
        }
    
    def compress_state(self, state: Dict[str, Any], algorithm: str = 'auto') -> Tuple[bytes, str]:
        """
        Compress state data using specified or optimal algorithm.
        
        Args:
            state: State dictionary to compress
            algorithm: Compression algorithm ('auto', 'gzip', 'zlib', 'pickle')
            
        Returns:
            Tuple of (compressed_data, algorithm_used)
        """
        start_time = time.time()
        
        try:
            # Clean state for serialization
            cleaned_state = self._clean_state_for_compression(state)
            
            # Auto-select algorithm if needed
            if algorithm == 'auto':
                algorithm = self._select_optimal_algorithm(cleaned_state)
            
            # Compress using selected algorithm
            compressed_data = self.compression_algorithms[algorithm](cleaned_state)
            
            # Update metrics
            compression_time = time.time() - start_time
            self.metrics.compressions += 1
            self.metrics.total_compression_time += compression_time
            
            # Calculate compression ratio
            original_size = len(json.dumps(cleaned_state, default=str).encode())
            compressed_size = len(compressed_data)
            ratio = compressed_size / original_size if original_size > 0 else 1.0
            self.metrics.compression_ratio = (
                (self.metrics.compression_ratio * (self.metrics.compressions - 1) + ratio)
                / self.metrics.compressions
            )
            
            logger.debug(f"State compressed using {algorithm}: "
                        f"{original_size} -> {compressed_size} bytes "
                        f"({ratio:.2%} ratio) in {compression_time:.3f}s")
            
            return compressed_data, algorithm
            
        except Exception as e:
            logger.error(f"State compression failed: {e}")
            # Fallback to pickle
            return pickle.dumps(state), 'pickle'
    
    def decompress_state(self, compressed_data: bytes, algorithm: str) -> Dict[str, Any]:
        """
        Decompress state data using specified algorithm.
        
        Args:
            compressed_data: Compressed state data
            algorithm: Algorithm used for compression
            
        Returns:
            Decompressed state dictionary
        """
        start_time = time.time()
        
        try:
            # Decompress using specified algorithm
            state = self.decompression_algorithms[algorithm](compressed_data)
            
            # Update metrics
            decompression_time = time.time() - start_time
            self.metrics.decompressions += 1
            self.metrics.total_decompression_time += decompression_time
            
            logger.debug(f"State decompressed using {algorithm} in {decompression_time:.3f}s")
            
            return state
            
        except Exception as e:
            logger.error(f"State decompression failed with {algorithm}: {e}")
            raise
    
    def _clean_state_for_compression(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Clean state by removing non-serializable objects."""
        cleaned_state = {}
        
        for key, value in state.items():
            try:
                if callable(value):
                    # Skip function objects
                    continue
                elif isinstance(value, set):
                    # Convert sets to lists
                    cleaned_state[key] = list(value)
                elif key in ['stream_callback', 'websocket_connections']:
                    # Skip known non-serializable keys
                    continue
                else:
                    # Test serializability
                    json.dumps(value, default=str)
                    cleaned_state[key] = value
            except (TypeError, ValueError):
                # Skip non-serializable values
                logger.debug(f"Skipping non-serializable state key: {key}")
                continue
        
        return cleaned_state
    
    def _select_optimal_algorithm(self, state: Dict[str, Any]) -> str:
        """Select optimal compression algorithm based on state characteristics."""
        state_size = len(json.dumps(state, default=str))
        
        # For small states, use zlib (faster)
        if state_size < 1024:  # 1KB
            return 'zlib'
        # For medium states, use gzip (good balance)
        elif state_size < 10240:  # 10KB
            return 'gzip'
        # For large states, use pickle (handles complex objects)
        else:
            return 'pickle'
    
    def _gzip_compress(self, state: Dict[str, Any]) -> bytes:
        """Compress state using gzip."""
        json_data = json.dumps(state, default=str).encode('utf-8')
        return gzip.compress(json_data)
    
    def _gzip_decompress(self, compressed_data: bytes) -> Dict[str, Any]:
        """Decompress state using gzip."""
        json_data = gzip.decompress(compressed_data).decode('utf-8')
        return json.loads(json_data)
    
    def _zlib_compress(self, state: Dict[str, Any]) -> bytes:
        """Compress state using zlib."""
        json_data = json.dumps(state, default=str).encode('utf-8')
        return zlib.compress(json_data)
    
    def _zlib_decompress(self, compressed_data: bytes) -> Dict[str, Any]:
        """Decompress state using zlib."""
        json_data = zlib.decompress(compressed_data).decode('utf-8')
        return json.loads(json_data)
    
    def _pickle_compress(self, state: Dict[str, Any]) -> bytes:
        """Compress state using pickle."""
        return gzip.compress(pickle.dumps(state))
    
    def _pickle_decompress(self, compressed_data: bytes) -> Dict[str, Any]:
        """Decompress state using pickle."""
        return pickle.loads(gzip.decompress(compressed_data))


class MultiLevelStateCache:
    """
    Multi-level state caching system with memory and Redis layers.
    
    Provides intelligent caching with automatic eviction,
    cache warming, and performance optimization.
    """
    
    def __init__(self, memory_cache_size: int = 1000, redis_ttl: int = 3600):
        self.memory_cache: Dict[str, Tuple[Dict[str, Any], datetime]] = {}
        self.memory_cache_size = memory_cache_size
        self.redis_ttl = redis_ttl
        self.compressor = StateCompressor()
        
        # Cache statistics
        self.memory_hits = 0
        self.memory_misses = 0
        self.redis_hits = 0
        self.redis_misses = 0
        
        # Cache key prefix
        self.cache_prefix = "state_cache:"
    
    async def get_state(self, state_id: str) -> Optional[Dict[str, Any]]:
        """Get state from cache (memory first, then Redis)."""
        # Try memory cache first
        if state_id in self.memory_cache:
            state, timestamp = self.memory_cache[state_id]
            # Check if still valid (5 minutes)
            if (datetime.now() - timestamp).total_seconds() < 300:
                self.memory_hits += 1
                logger.debug(f"State cache hit (memory): {state_id}")
                return state
            else:
                # Remove expired entry
                del self.memory_cache[state_id]
        
        self.memory_misses += 1
        
        # Try Redis cache
        if redis_client:
            try:
                cache_key = f"{self.cache_prefix}{state_id}"
                cached_data = redis_client.get(cache_key)
                
                if cached_data:
                    # Decompress and deserialize
                    cache_info = json.loads(cached_data)
                    compressed_data = bytes.fromhex(cache_info['data'])
                    algorithm = cache_info['algorithm']
                    
                    state = self.compressor.decompress_state(compressed_data, algorithm)
                    
                    # Store in memory cache for faster access
                    self._store_in_memory_cache(state_id, state)
                    
                    self.redis_hits += 1
                    logger.debug(f"State cache hit (Redis): {state_id}")
                    return state
                
            except Exception as e:
                logger.warning(f"Redis cache retrieval failed for {state_id}: {e}")
        
        self.redis_misses += 1
        return None
    
    async def store_state(self, state_id: str, state: Dict[str, Any]) -> bool:
        """Store state in cache (both memory and Redis)."""
        try:
            # Store in memory cache
            self._store_in_memory_cache(state_id, state)
            
            # Store in Redis cache
            if redis_client:
                try:
                    # Compress state
                    compressed_data, algorithm = self.compressor.compress_state(state)
                    
                    # Create cache entry
                    cache_info = {
                        'data': compressed_data.hex(),
                        'algorithm': algorithm,
                        'timestamp': datetime.now().isoformat(),
                        'size': len(compressed_data)
                    }
                    
                    cache_key = f"{self.cache_prefix}{state_id}"
                    redis_client.setex(
                        cache_key,
                        self.redis_ttl,
                        json.dumps(cache_info)
                    )
                    
                    logger.debug(f"State cached (Redis): {state_id}")
                    
                except Exception as e:
                    logger.warning(f"Redis cache storage failed for {state_id}: {e}")
            
            return True
            
        except Exception as e:
            logger.error(f"State cache storage failed for {state_id}: {e}")
            return False
    
    def _store_in_memory_cache(self, state_id: str, state: Dict[str, Any]):
        """Store state in memory cache with size management."""
        # Remove oldest entries if cache is full
        if len(self.memory_cache) >= self.memory_cache_size:
            # Remove 10% of oldest entries
            entries_to_remove = max(1, self.memory_cache_size // 10)
            oldest_keys = sorted(
                self.memory_cache.keys(),
                key=lambda k: self.memory_cache[k][1]
            )[:entries_to_remove]
            
            for key in oldest_keys:
                del self.memory_cache[key]
        
        # Store new entry
        self.memory_cache[state_id] = (state, datetime.now())
    
    async def invalidate_state(self, state_id: str) -> bool:
        """Invalidate state in all cache levels."""
        # Remove from memory cache
        if state_id in self.memory_cache:
            del self.memory_cache[state_id]
        
        # Remove from Redis cache
        if redis_client:
            try:
                cache_key = f"{self.cache_prefix}{state_id}"
                redis_client.delete(cache_key)
                return True
            except Exception as e:
                logger.warning(f"Redis cache invalidation failed for {state_id}: {e}")
        
        return False
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics."""
        total_memory_requests = self.memory_hits + self.memory_misses
        total_redis_requests = self.redis_hits + self.redis_misses
        
        return {
            'memory_cache': {
                'size': len(self.memory_cache),
                'max_size': self.memory_cache_size,
                'hits': self.memory_hits,
                'misses': self.memory_misses,
                'hit_rate': (self.memory_hits / total_memory_requests * 100) 
                           if total_memory_requests > 0 else 0
            },
            'redis_cache': {
                'hits': self.redis_hits,
                'misses': self.redis_misses,
                'hit_rate': (self.redis_hits / total_redis_requests * 100) 
                           if total_redis_requests > 0 else 0
            },
            'compression': {
                'compressions': self.compressor.metrics.compressions,
                'decompressions': self.compressor.metrics.decompressions,
                'avg_compression_ratio': self.compressor.metrics.compression_ratio,
                'avg_compression_time': (
                    self.compressor.metrics.total_compression_time / 
                    self.compressor.metrics.compressions
                ) if self.compressor.metrics.compressions > 0 else 0
            }
        }


class StateDeltaManager:
    """
    State delta management for incremental updates.
    
    Tracks state changes and applies only deltas to reduce
    serialization overhead and improve performance.
    """
    
    def __init__(self):
        self.state_snapshots: Dict[str, Dict[str, Any]] = {}
        self.delta_history: Dict[str, List[Dict[str, Any]]] = {}
    
    def create_delta(self, state_id: str, new_state: Dict[str, Any]) -> Dict[str, Any]:
        """Create delta between current and previous state."""
        if state_id not in self.state_snapshots:
            # First state, store as snapshot
            self.state_snapshots[state_id] = new_state.copy()
            return {'type': 'full_state', 'data': new_state}
        
        previous_state = self.state_snapshots[state_id]
        delta = self._calculate_delta(previous_state, new_state)
        
        # Update snapshot
        self.state_snapshots[state_id] = new_state.copy()
        
        # Store delta in history
        if state_id not in self.delta_history:
            self.delta_history[state_id] = []
        self.delta_history[state_id].append(delta)
        
        # Keep only last 10 deltas
        if len(self.delta_history[state_id]) > 10:
            self.delta_history[state_id] = self.delta_history[state_id][-10:]
        
        return {'type': 'delta', 'data': delta}
    
    def _calculate_delta(self, old_state: Dict[str, Any], new_state: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate delta between two states."""
        delta = {
            'added': {},
            'modified': {},
            'removed': []
        }
        
        # Find added and modified keys
        for key, value in new_state.items():
            if key not in old_state:
                delta['added'][key] = value
            elif old_state[key] != value:
                delta['modified'][key] = value
        
        # Find removed keys
        for key in old_state:
            if key not in new_state:
                delta['removed'].append(key)
        
        return delta
    
    def apply_delta(self, state_id: str, delta: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Apply delta to reconstruct state."""
        if state_id not in self.state_snapshots:
            return None
        
        state = self.state_snapshots[state_id].copy()
        
        # Apply delta
        for key, value in delta.get('added', {}).items():
            state[key] = value
        
        for key, value in delta.get('modified', {}).items():
            state[key] = value
        
        for key in delta.get('removed', []):
            state.pop(key, None)
        
        return state


# Global instances
state_compressor = StateCompressor()
state_cache = MultiLevelStateCache()
delta_manager = StateDeltaManager()


async def get_state_performance_metrics() -> Dict[str, Any]:
    """Get comprehensive state management performance metrics."""
    return {
        'cache_stats': state_cache.get_cache_stats(),
        'compression_stats': {
            'compressions': state_compressor.metrics.compressions,
            'decompressions': state_compressor.metrics.decompressions,
            'avg_compression_time': (
                state_compressor.metrics.total_compression_time / 
                state_compressor.metrics.compressions
            ) if state_compressor.metrics.compressions > 0 else 0,
            'avg_compression_ratio': state_compressor.metrics.compression_ratio
        },
        'delta_stats': {
            'tracked_states': len(delta_manager.state_snapshots),
            'total_deltas': sum(len(deltas) for deltas in delta_manager.delta_history.values())
        }
    }
