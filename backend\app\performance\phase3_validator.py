"""
Phase 3 Performance Validation and Monitoring System.

This module provides comprehensive validation and monitoring for all
Phase 3 performance optimizations, including:
- Performance baseline establishment
- Optimization impact measurement
- Continuous performance monitoring
- Performance regression detection
"""

import logging
import asyncio
import time
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import statistics

from .database_optimization import db_optimizer, batch_manager, query_cache
from .connection_pool_optimizer import pool_optimizer
from .async_optimizer import async_file_manager, concurrent_agent_loader, async_task_manager
from .state_optimizer import state_cache, state_compressor, delta_manager
from .multi_level_cache import multi_cache, cache_warmer, invalidation_manager
from .optimized_crud import optimized_crud
from .index_optimization import index_optimizer

logger = logging.getLogger(__name__)


@dataclass
class PerformanceBaseline:
    """Performance baseline metrics for comparison."""
    avg_query_time: float = 0.0
    cache_hit_rate: float = 0.0
    connection_pool_utilization: float = 0.0
    state_compression_ratio: float = 0.0
    async_operation_time: float = 0.0
    memory_usage_mb: float = 0.0
    throughput_requests_per_second: float = 0.0
    error_rate_percent: float = 0.0
    established_at: datetime = None


@dataclass
class PerformanceMetrics:
    """Current performance metrics."""
    database_performance: Dict[str, Any]
    connection_pool_performance: Dict[str, Any]
    async_operations_performance: Dict[str, Any]
    state_management_performance: Dict[str, Any]
    caching_performance: Dict[str, Any]
    overall_performance: Dict[str, Any]
    measured_at: datetime


class Phase3PerformanceValidator:
    """
    Comprehensive performance validation system for Phase 3 optimizations.
    
    Provides baseline establishment, performance measurement, and
    continuous monitoring capabilities.
    """
    
    def __init__(self):
        self.baseline: Optional[PerformanceBaseline] = None
        self.performance_history: List[PerformanceMetrics] = []
        self.monitoring_enabled = True
        self.alert_thresholds = {
            'query_time_degradation': 50.0,  # 50% increase
            'cache_hit_rate_drop': 10.0,      # 10% drop
            'error_rate_increase': 5.0,       # 5% increase
            'memory_usage_increase': 25.0     # 25% increase
        }
    
    async def establish_baseline(self) -> PerformanceBaseline:
        """Establish performance baseline before optimizations."""
        logger.info("Establishing Phase 3 performance baseline...")
        
        try:
            # Collect baseline metrics
            baseline_data = await self._collect_comprehensive_metrics()
            
            self.baseline = PerformanceBaseline(
                avg_query_time=baseline_data['database']['avg_query_time'],
                cache_hit_rate=baseline_data['caching']['overall_hit_rate'],
                connection_pool_utilization=baseline_data['connection_pool']['utilization_percent'],
                state_compression_ratio=baseline_data['state_management']['compression_ratio'],
                async_operation_time=baseline_data['async_operations']['avg_operation_time'],
                memory_usage_mb=baseline_data['overall']['memory_usage_mb'],
                throughput_requests_per_second=baseline_data['overall']['throughput_rps'],
                error_rate_percent=baseline_data['overall']['error_rate_percent'],
                established_at=datetime.now()
            )
            
            logger.info(f"Performance baseline established: {asdict(self.baseline)}")
            return self.baseline
            
        except Exception as e:
            logger.error(f"Failed to establish performance baseline: {e}")
            raise
    
    async def validate_optimizations(self) -> Dict[str, Any]:
        """Validate that Phase 3 optimizations provide measurable improvements."""
        if not self.baseline:
            raise ValueError("Performance baseline not established. Call establish_baseline() first.")
        
        logger.info("Validating Phase 3 optimization improvements...")
        
        try:
            # Collect current metrics
            current_data = await self._collect_comprehensive_metrics()
            
            # Calculate improvements
            improvements = self._calculate_improvements(current_data)
            
            # Validate against targets
            validation_results = self._validate_against_targets(improvements)
            
            # Store current metrics
            current_metrics = PerformanceMetrics(
                database_performance=current_data['database'],
                connection_pool_performance=current_data['connection_pool'],
                async_operations_performance=current_data['async_operations'],
                state_management_performance=current_data['state_management'],
                caching_performance=current_data['caching'],
                overall_performance=current_data['overall'],
                measured_at=datetime.now()
            )
            
            self.performance_history.append(current_metrics)
            
            # Keep only last 100 measurements
            if len(self.performance_history) > 100:
                self.performance_history = self.performance_history[-100:]
            
            logger.info(f"Optimization validation completed: {validation_results['summary']}")
            return validation_results
            
        except Exception as e:
            logger.error(f"Optimization validation failed: {e}")
            raise
    
    async def _collect_comprehensive_metrics(self) -> Dict[str, Any]:
        """Collect comprehensive performance metrics from all systems."""
        metrics = {}
        
        try:
            # Database performance metrics
            db_metrics = db_optimizer.get_performance_metrics()
            batch_metrics = batch_manager.get_performance_metrics()
            cache_metrics = query_cache.get_cache_stats()
            
            metrics['database'] = {
                'avg_query_time': db_metrics.get('avg_query_time', 0.0),
                'slow_query_count': db_metrics.get('slow_queries', 0),
                'total_queries': db_metrics.get('total_queries', 0),
                'batch_operations': batch_metrics,
                'query_cache_hit_rate': cache_metrics.get('hit_rate_percent', 0.0)
            }
            
            # Connection pool performance
            pool_metrics = pool_optimizer.get_performance_metrics()
            metrics['connection_pool'] = {
                'utilization_percent': pool_metrics['pool_metrics'].get('utilization_percent', 0.0),
                'avg_checkout_time': pool_metrics['performance_metrics'].get('avg_checkout_time', 0.0),
                'connection_timeouts': pool_metrics['performance_metrics'].get('connection_timeouts', 0),
                'healthy_connections': pool_metrics['health_metrics'].get('healthy_connections', 0),
                'connection_leaks': pool_metrics['health_metrics'].get('connection_leaks', 0)
            }
            
            # Async operations performance
            file_metrics = async_file_manager.get_performance_metrics()
            loader_metrics = concurrent_agent_loader.get_loading_metrics()
            task_metrics = async_task_manager.get_task_status()
            
            metrics['async_operations'] = {
                'avg_file_read_time': file_metrics.get('avg_read_time', 0.0),
                'avg_file_write_time': file_metrics.get('avg_write_time', 0.0),
                'avg_agent_loading_time': loader_metrics.get('avg_loading_time', 0.0),
                'active_tasks': task_metrics.get('active_tasks', 0),
                'avg_operation_time': (
                    file_metrics.get('avg_read_time', 0.0) + 
                    file_metrics.get('avg_write_time', 0.0) + 
                    loader_metrics.get('avg_loading_time', 0.0)
                ) / 3
            }
            
            # State management performance
            state_cache_stats = state_cache.get_cache_stats()
            
            metrics['state_management'] = {
                'cache_hit_rate': state_cache_stats['memory_cache'].get('hit_rate', 0.0),
                'compression_ratio': state_compressor.metrics.compression_ratio,
                'avg_compression_time': (
                    state_compressor.metrics.total_compression_time / 
                    state_compressor.metrics.compressions
                ) if state_compressor.metrics.compressions > 0 else 0.0,
                'cache_size': state_cache_stats['memory_cache'].get('size', 0)
            }
            
            # Multi-level caching performance
            cache_stats = multi_cache.get_metrics()
            
            metrics['caching'] = {
                'overall_hit_rate': cache_stats['hit_rates'].get('overall', 0.0),
                'l1_hit_rate': cache_stats['hit_rates'].get('l1', 0.0),
                'l2_hit_rate': cache_stats['hit_rates'].get('l2', 0.0),
                'avg_response_time': cache_stats['performance'].get('avg_response_time', 0.0),
                'total_requests': cache_stats['requests'].get('total', 0),
                'cache_size': cache_stats['cache_sizes'].get('l1_entries', 0)
            }
            
            # Overall system performance
            metrics['overall'] = {
                'memory_usage_mb': self._get_memory_usage(),
                'throughput_rps': self._calculate_throughput(),
                'error_rate_percent': self._calculate_error_rate(),
                'response_time_p95': self._calculate_p95_response_time()
            }
            
        except Exception as e:
            logger.error(f"Error collecting performance metrics: {e}")
            # Return partial metrics if some collection fails
        
        return metrics
    
    def _calculate_improvements(self, current_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate performance improvements compared to baseline."""
        if not self.baseline:
            return {}
        
        improvements = {}
        
        # Database improvements
        query_time_improvement = (
            (self.baseline.avg_query_time - current_data['database']['avg_query_time']) /
            self.baseline.avg_query_time * 100
        ) if self.baseline.avg_query_time > 0 else 0
        
        improvements['database'] = {
            'query_time_improvement_percent': query_time_improvement,
            'cache_hit_rate_improvement': (
                current_data['database']['query_cache_hit_rate'] - 
                (self.baseline.cache_hit_rate or 0)
            )
        }
        
        # Connection pool improvements
        improvements['connection_pool'] = {
            'utilization_optimization': abs(
                current_data['connection_pool']['utilization_percent'] - 75.0
            ),  # Target 75% utilization
            'timeout_reduction': self.baseline.avg_query_time - current_data['connection_pool']['avg_checkout_time']
        }
        
        # Async operations improvements
        async_improvement = (
            (self.baseline.async_operation_time - current_data['async_operations']['avg_operation_time']) /
            self.baseline.async_operation_time * 100
        ) if self.baseline.async_operation_time > 0 else 0
        
        improvements['async_operations'] = {
            'operation_time_improvement_percent': async_improvement
        }
        
        # State management improvements
        improvements['state_management'] = {
            'compression_ratio_improvement': (
                self.baseline.state_compression_ratio - 
                current_data['state_management']['compression_ratio']
            ),  # Lower is better for compression ratio
            'cache_hit_rate_improvement': (
                current_data['state_management']['cache_hit_rate'] - 
                (self.baseline.cache_hit_rate or 0)
            )
        }
        
        # Caching improvements
        improvements['caching'] = {
            'hit_rate_improvement': (
                current_data['caching']['overall_hit_rate'] - 
                (self.baseline.cache_hit_rate or 0)
            ),
            'response_time_improvement': (
                self.baseline.avg_query_time - 
                current_data['caching']['avg_response_time']
            )
        }
        
        # Overall improvements
        memory_improvement = (
            (self.baseline.memory_usage_mb - current_data['overall']['memory_usage_mb']) /
            self.baseline.memory_usage_mb * 100
        ) if self.baseline.memory_usage_mb > 0 else 0
        
        throughput_improvement = (
            (current_data['overall']['throughput_rps'] - self.baseline.throughput_requests_per_second) /
            self.baseline.throughput_requests_per_second * 100
        ) if self.baseline.throughput_requests_per_second > 0 else 0
        
        improvements['overall'] = {
            'memory_usage_improvement_percent': memory_improvement,
            'throughput_improvement_percent': throughput_improvement,
            'error_rate_improvement': (
                self.baseline.error_rate_percent - 
                current_data['overall']['error_rate_percent']
            )
        }
        
        return improvements
    
    def _validate_against_targets(self, improvements: Dict[str, Any]) -> Dict[str, Any]:
        """Validate improvements against Phase 3 targets."""
        validation_results = {
            'passed': [],
            'failed': [],
            'warnings': [],
            'summary': {}
        }
        
        # Define Phase 3 targets
        targets = {
            'query_time_improvement': 30.0,      # 30% improvement
            'cache_hit_rate_minimum': 80.0,      # 80% hit rate
            'memory_usage_reduction': 20.0,      # 20% reduction
            'throughput_improvement': 25.0,      # 25% improvement
            'error_rate_maximum': 2.0            # Max 2% error rate
        }
        
        # Validate each target
        if improvements.get('database', {}).get('query_time_improvement_percent', 0) >= targets['query_time_improvement']:
            validation_results['passed'].append('Database query time improvement target met')
        else:
            validation_results['failed'].append('Database query time improvement target not met')
        
        if improvements.get('caching', {}).get('hit_rate_improvement', 0) >= targets['cache_hit_rate_minimum']:
            validation_results['passed'].append('Cache hit rate target met')
        else:
            validation_results['warnings'].append('Cache hit rate could be improved')
        
        if improvements.get('overall', {}).get('memory_usage_improvement_percent', 0) >= targets['memory_usage_reduction']:
            validation_results['passed'].append('Memory usage reduction target met')
        else:
            validation_results['failed'].append('Memory usage reduction target not met')
        
        if improvements.get('overall', {}).get('throughput_improvement_percent', 0) >= targets['throughput_improvement']:
            validation_results['passed'].append('Throughput improvement target met')
        else:
            validation_results['warnings'].append('Throughput improvement could be better')
        
        # Calculate overall success rate
        total_targets = len(targets)
        passed_targets = len(validation_results['passed'])
        success_rate = (passed_targets / total_targets) * 100
        
        validation_results['summary'] = {
            'success_rate_percent': success_rate,
            'targets_passed': passed_targets,
            'total_targets': total_targets,
            'overall_status': 'PASSED' if success_rate >= 80 else 'NEEDS_IMPROVEMENT'
        }
        
        return validation_results
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024  # Convert to MB
        except ImportError:
            return 0.0
    
    def _calculate_throughput(self) -> float:
        """Calculate current throughput in requests per second."""
        # This would be calculated based on recent request metrics
        # For now, return a placeholder value
        return 100.0
    
    def _calculate_error_rate(self) -> float:
        """Calculate current error rate percentage."""
        # This would be calculated based on recent error metrics
        # For now, return a placeholder value
        return 1.0
    
    def _calculate_p95_response_time(self) -> float:
        """Calculate 95th percentile response time."""
        # This would be calculated from response time history
        # For now, return a placeholder value
        return 0.1
    
    async def generate_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        if not self.baseline:
            return {'error': 'No baseline established'}
        
        current_metrics = await self._collect_comprehensive_metrics()
        improvements = self._calculate_improvements(current_metrics)
        validation = self._validate_against_targets(improvements)
        
        report = {
            'report_generated_at': datetime.now().isoformat(),
            'baseline_established_at': self.baseline.established_at.isoformat(),
            'baseline_metrics': asdict(self.baseline),
            'current_metrics': current_metrics,
            'improvements': improvements,
            'validation_results': validation,
            'recommendations': self._generate_recommendations(validation, improvements),
            'performance_trends': self._analyze_performance_trends()
        }
        
        return report
    
    def _generate_recommendations(self, validation: Dict[str, Any], improvements: Dict[str, Any]) -> List[str]:
        """Generate performance improvement recommendations."""
        recommendations = []
        
        if len(validation['failed']) > 0:
            recommendations.append("Review failed optimization targets and consider additional tuning")
        
        if improvements.get('database', {}).get('query_time_improvement_percent', 0) < 20:
            recommendations.append("Consider additional database query optimizations")
        
        if improvements.get('caching', {}).get('hit_rate_improvement', 0) < 70:
            recommendations.append("Review cache warming strategies and cache key patterns")
        
        if improvements.get('overall', {}).get('memory_usage_improvement_percent', 0) < 15:
            recommendations.append("Investigate memory usage patterns and consider additional optimizations")
        
        return recommendations
    
    def _analyze_performance_trends(self) -> Dict[str, Any]:
        """Analyze performance trends over time."""
        if len(self.performance_history) < 2:
            return {'status': 'Insufficient data for trend analysis'}
        
        # Calculate trends for key metrics
        recent_metrics = self.performance_history[-10:]  # Last 10 measurements
        
        query_times = [m.database_performance.get('avg_query_time', 0) for m in recent_metrics]
        cache_hit_rates = [m.caching_performance.get('overall_hit_rate', 0) for m in recent_metrics]
        
        trends = {
            'query_time_trend': 'improving' if len(query_times) > 1 and query_times[-1] < query_times[0] else 'stable',
            'cache_hit_rate_trend': 'improving' if len(cache_hit_rates) > 1 and cache_hit_rates[-1] > cache_hit_rates[0] else 'stable',
            'measurements_count': len(self.performance_history),
            'trend_period_hours': (
                (self.performance_history[-1].measured_at - self.performance_history[0].measured_at).total_seconds() / 3600
            ) if len(self.performance_history) > 1 else 0
        }
        
        return trends


# Global performance validator instance
performance_validator = Phase3PerformanceValidator()
