"""
Connection pool optimization for Phase 3 performance improvements.

This module provides enhanced connection pool management with:
- Dynamic pool sizing based on load
- Connection health monitoring and validation
- Pool metrics and performance analysis
- Automatic connection leak detection and cleanup
"""

import logging
import asyncio
import time
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from contextlib import asynccontextmanager
from sqlalchemy import text, event
from sqlalchemy.engine import Engine
from sqlalchemy.orm import Session
from sqlalchemy.pool import Pool

from ..database import engine, get_db
from ..redis_client import redis_client

logger = logging.getLogger(__name__)


@dataclass
class ConnectionMetrics:
    """Metrics for individual database connections."""
    connection_id: str
    created_at: datetime
    last_used: datetime
    query_count: int = 0
    total_query_time: float = 0.0
    avg_query_time: float = 0.0
    error_count: int = 0
    is_healthy: bool = True
    health_check_count: int = 0
    last_health_check: Optional[datetime] = None


@dataclass
class PoolMetrics:
    """Comprehensive pool performance metrics."""
    pool_size: int = 0
    max_overflow: int = 0
    checked_out: int = 0
    overflow: int = 0
    invalid: int = 0
    total_connections: int = 0
    active_connections: int = 0
    idle_connections: int = 0
    
    # Performance metrics
    avg_checkout_time: float = 0.0
    max_checkout_time: float = 0.0
    total_checkouts: int = 0
    failed_checkouts: int = 0
    connection_timeouts: int = 0
    
    # Health metrics
    healthy_connections: int = 0
    unhealthy_connections: int = 0
    connection_leaks: int = 0
    recycled_connections: int = 0
    
    # Query metrics
    total_queries: int = 0
    slow_queries: int = 0
    failed_queries: int = 0
    avg_query_time: float = 0.0
    
    # Timestamps
    last_updated: datetime = field(default_factory=datetime.now)


class ConnectionPoolOptimizer:
    """
    Advanced connection pool optimizer with dynamic sizing and health monitoring.
    
    Provides intelligent connection pool management that adapts to load patterns
    and maintains optimal performance through continuous monitoring.
    """
    
    def __init__(self, engine: Engine):
        self.engine = engine
        self.pool = engine.pool
        self.metrics = PoolMetrics()
        self.connection_metrics: Dict[str, ConnectionMetrics] = {}
        
        # Configuration
        self.health_check_interval = 300  # 5 minutes
        self.slow_query_threshold = 1.0  # 1 second
        self.connection_timeout_threshold = 30.0  # 30 seconds
        self.leak_detection_threshold = 100  # connections
        
        # Dynamic sizing
        self.auto_scaling_enabled = True
        self.min_pool_size = 5
        self.max_pool_size = 50
        self.scale_up_threshold = 0.8  # 80% utilization
        self.scale_down_threshold = 0.3  # 30% utilization
        
        # Monitoring
        self.monitoring_enabled = True
        self.last_health_check = datetime.now()
        self.checkout_times: Dict[str, float] = {}
        
        # Register event listeners
        self._register_pool_events()
    
    def _register_pool_events(self):
        """Register SQLAlchemy pool event listeners for monitoring."""
        
        @event.listens_for(self.pool, "connect")
        def on_connect(dbapi_connection, connection_record):
            """Handle new connection creation."""
            connection_id = str(id(dbapi_connection))
            
            self.connection_metrics[connection_id] = ConnectionMetrics(
                connection_id=connection_id,
                created_at=datetime.now(),
                last_used=datetime.now()
            )
            
            self.metrics.total_connections += 1
            logger.debug(f"New connection created: {connection_id}")
        
        @event.listens_for(self.pool, "checkout")
        def on_checkout(dbapi_connection, connection_record, connection_proxy):
            """Handle connection checkout from pool."""
            connection_id = str(id(dbapi_connection))
            checkout_time = time.time()
            
            self.checkout_times[connection_id] = checkout_time
            self.metrics.total_checkouts += 1
            
            if connection_id in self.connection_metrics:
                self.connection_metrics[connection_id].last_used = datetime.now()
        
        @event.listens_for(self.pool, "checkin")
        def on_checkin(dbapi_connection, connection_record):
            """Handle connection checkin to pool."""
            connection_id = str(id(dbapi_connection))
            
            # Calculate checkout duration
            if connection_id in self.checkout_times:
                checkout_duration = time.time() - self.checkout_times[connection_id]
                self._update_checkout_metrics(checkout_duration)
                del self.checkout_times[connection_id]
        
        @event.listens_for(self.pool, "invalidate")
        def on_invalidate(dbapi_connection, connection_record, exception):
            """Handle connection invalidation."""
            connection_id = str(id(dbapi_connection))
            
            if connection_id in self.connection_metrics:
                self.connection_metrics[connection_id].is_healthy = False
                self.connection_metrics[connection_id].error_count += 1
            
            self.metrics.invalid += 1
            logger.warning(f"Connection invalidated: {connection_id}, reason: {exception}")
    
    def _update_checkout_metrics(self, duration: float):
        """Update checkout time metrics."""
        self.metrics.avg_checkout_time = (
            (self.metrics.avg_checkout_time * (self.metrics.total_checkouts - 1) + duration)
            / self.metrics.total_checkouts
        )
        
        if duration > self.metrics.max_checkout_time:
            self.metrics.max_checkout_time = duration
        
        if duration > self.connection_timeout_threshold:
            self.metrics.connection_timeouts += 1
    
    async def perform_health_checks(self) -> Dict[str, Any]:
        """Perform comprehensive health checks on all connections."""
        health_results = {
            "healthy_connections": 0,
            "unhealthy_connections": 0,
            "total_checked": 0,
            "errors": []
        }
        
        try:
            # Update pool statistics
            self._update_pool_statistics()
            
            # Check for connection leaks
            leak_count = self._detect_connection_leaks()
            if leak_count > 0:
                health_results["errors"].append(f"Detected {leak_count} potential connection leaks")
            
            # Validate connection health
            for connection_id, conn_metrics in self.connection_metrics.items():
                try:
                    is_healthy = await self._validate_connection_health(connection_id)
                    conn_metrics.is_healthy = is_healthy
                    conn_metrics.health_check_count += 1
                    conn_metrics.last_health_check = datetime.now()
                    
                    if is_healthy:
                        health_results["healthy_connections"] += 1
                    else:
                        health_results["unhealthy_connections"] += 1
                    
                    health_results["total_checked"] += 1
                    
                except Exception as e:
                    health_results["errors"].append(f"Health check failed for {connection_id}: {str(e)}")
            
            # Update metrics
            self.metrics.healthy_connections = health_results["healthy_connections"]
            self.metrics.unhealthy_connections = health_results["unhealthy_connections"]
            self.last_health_check = datetime.now()
            
            # Consider dynamic scaling
            if self.auto_scaling_enabled:
                await self._consider_pool_scaling()
            
            logger.info(f"Health check completed: {health_results['healthy_connections']} healthy, "
                       f"{health_results['unhealthy_connections']} unhealthy connections")
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            health_results["errors"].append(str(e))
        
        return health_results
    
    async def _validate_connection_health(self, connection_id: str) -> bool:
        """Validate the health of a specific connection."""
        try:
            # Use a simple query to test connection health
            db = next(get_db())
            try:
                result = db.execute(text("SELECT 1")).fetchone()
                return result is not None
            finally:
                db.close()
        except Exception as e:
            logger.warning(f"Connection {connection_id} health check failed: {e}")
            return False
    
    def _update_pool_statistics(self):
        """Update current pool statistics."""
        try:
            pool_status = self.pool.status()
            
            self.metrics.pool_size = self.pool.size()
            self.metrics.checked_out = self.pool.checkedout()
            self.metrics.overflow = self.pool.overflow()
            self.metrics.invalid = self.pool.invalidated()
            
            # Calculate derived metrics
            self.metrics.active_connections = self.metrics.checked_out
            self.metrics.idle_connections = self.metrics.pool_size - self.metrics.checked_out
            self.metrics.last_updated = datetime.now()
            
        except Exception as e:
            logger.warning(f"Failed to update pool statistics: {e}")
    
    def _detect_connection_leaks(self) -> int:
        """Detect potential connection leaks."""
        leak_count = 0
        current_time = datetime.now()
        
        for connection_id, conn_metrics in self.connection_metrics.items():
            # Check for connections that have been active too long
            if (current_time - conn_metrics.last_used).total_seconds() > 3600:  # 1 hour
                leak_count += 1
                logger.warning(f"Potential connection leak detected: {connection_id}")
        
        self.metrics.connection_leaks = leak_count
        return leak_count
    
    async def _consider_pool_scaling(self):
        """Consider scaling the connection pool based on current metrics."""
        try:
            utilization = self.metrics.checked_out / self.metrics.pool_size if self.metrics.pool_size > 0 else 0
            
            # Scale up if utilization is high
            if utilization > self.scale_up_threshold and self.metrics.pool_size < self.max_pool_size:
                new_size = min(self.metrics.pool_size + 5, self.max_pool_size)
                await self._resize_pool(new_size)
                logger.info(f"Scaled up connection pool to {new_size} (utilization: {utilization:.2%})")
            
            # Scale down if utilization is low
            elif utilization < self.scale_down_threshold and self.metrics.pool_size > self.min_pool_size:
                new_size = max(self.metrics.pool_size - 2, self.min_pool_size)
                await self._resize_pool(new_size)
                logger.info(f"Scaled down connection pool to {new_size} (utilization: {utilization:.2%})")
                
        except Exception as e:
            logger.error(f"Pool scaling failed: {e}")
    
    async def _resize_pool(self, new_size: int):
        """Resize the connection pool (placeholder for actual implementation)."""
        # Note: SQLAlchemy doesn't support dynamic pool resizing out of the box
        # This would require engine recreation or custom pool implementation
        logger.info(f"Pool resize requested to {new_size} connections")
    
    def record_query_metrics(self, duration: float, success: bool = True):
        """Record query execution metrics."""
        self.metrics.total_queries += 1
        
        if not success:
            self.metrics.failed_queries += 1
        
        if duration > self.slow_query_threshold:
            self.metrics.slow_queries += 1
        
        # Update average query time
        self.metrics.avg_query_time = (
            (self.metrics.avg_query_time * (self.metrics.total_queries - 1) + duration)
            / self.metrics.total_queries
        )
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get comprehensive performance metrics."""
        self._update_pool_statistics()
        
        return {
            "pool_metrics": {
                "pool_size": self.metrics.pool_size,
                "max_overflow": self.metrics.max_overflow,
                "checked_out": self.metrics.checked_out,
                "overflow": self.metrics.overflow,
                "invalid": self.metrics.invalid,
                "active_connections": self.metrics.active_connections,
                "idle_connections": self.metrics.idle_connections,
                "utilization_percent": (self.metrics.checked_out / self.metrics.pool_size * 100) 
                                     if self.metrics.pool_size > 0 else 0
            },
            "performance_metrics": {
                "avg_checkout_time": self.metrics.avg_checkout_time,
                "max_checkout_time": self.metrics.max_checkout_time,
                "total_checkouts": self.metrics.total_checkouts,
                "failed_checkouts": self.metrics.failed_checkouts,
                "connection_timeouts": self.metrics.connection_timeouts,
                "avg_query_time": self.metrics.avg_query_time,
                "total_queries": self.metrics.total_queries,
                "slow_queries": self.metrics.slow_queries,
                "failed_queries": self.metrics.failed_queries,
                "slow_query_percentage": (self.metrics.slow_queries / self.metrics.total_queries * 100)
                                       if self.metrics.total_queries > 0 else 0
            },
            "health_metrics": {
                "healthy_connections": self.metrics.healthy_connections,
                "unhealthy_connections": self.metrics.unhealthy_connections,
                "connection_leaks": self.metrics.connection_leaks,
                "recycled_connections": self.metrics.recycled_connections,
                "last_health_check": self.last_health_check.isoformat()
            },
            "connection_details": [
                {
                    "connection_id": conn_id,
                    "created_at": metrics.created_at.isoformat(),
                    "last_used": metrics.last_used.isoformat(),
                    "query_count": metrics.query_count,
                    "avg_query_time": metrics.avg_query_time,
                    "error_count": metrics.error_count,
                    "is_healthy": metrics.is_healthy,
                    "health_check_count": metrics.health_check_count
                }
                for conn_id, metrics in self.connection_metrics.items()
            ]
        }
    
    async def optimize_pool_configuration(self) -> Dict[str, Any]:
        """Optimize pool configuration based on current metrics and patterns."""
        optimization_results = {
            "recommendations": [],
            "applied_changes": [],
            "warnings": []
        }
        
        try:
            # Analyze current performance
            metrics = self.get_performance_metrics()
            pool_metrics = metrics["pool_metrics"]
            perf_metrics = metrics["performance_metrics"]
            
            # Recommend pool size adjustments
            utilization = pool_metrics["utilization_percent"]
            if utilization > 90:
                optimization_results["recommendations"].append(
                    "Consider increasing pool size - high utilization detected"
                )
            elif utilization < 20:
                optimization_results["recommendations"].append(
                    "Consider decreasing pool size - low utilization detected"
                )
            
            # Recommend timeout adjustments
            if perf_metrics["connection_timeouts"] > 0:
                optimization_results["recommendations"].append(
                    "Consider increasing pool timeout - connection timeouts detected"
                )
            
            # Recommend query optimization
            if perf_metrics["slow_query_percentage"] > 10:
                optimization_results["recommendations"].append(
                    "Consider query optimization - high percentage of slow queries"
                )
            
            # Check for connection leaks
            if metrics["health_metrics"]["connection_leaks"] > 0:
                optimization_results["warnings"].append(
                    "Connection leaks detected - review connection management"
                )
            
            logger.info(f"Pool optimization analysis completed: {len(optimization_results['recommendations'])} recommendations")
            
        except Exception as e:
            logger.error(f"Pool optimization failed: {e}")
            optimization_results["warnings"].append(f"Optimization analysis failed: {str(e)}")
        
        return optimization_results


# Global connection pool optimizer instance
pool_optimizer = ConnectionPoolOptimizer(engine)
