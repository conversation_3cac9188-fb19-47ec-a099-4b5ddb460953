"""
Multi-level caching system for Phase 3 performance improvements.

This module provides a comprehensive caching solution with:
- L1 (Memory) cache for ultra-fast access
- L2 (Redis) cache for distributed caching
- L3 (Database) cache for persistent storage
- Intelligent cache warming and prefetching
- Advanced invalidation strategies
- Performance monitoring and analytics
"""

import logging
import asyncio
import time
import json
import pickle
import hashlib
import functools
from typing import Dict, Any, List, Optional, Union, Callable, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from collections import OrderedDict
import threading
from contextlib import asynccontextmanager

from ..redis_client import redis_client
from ..database import get_db

logger = logging.getLogger(__name__)


class CacheLevel(Enum):
    """Cache levels in the multi-level hierarchy."""
    L1_MEMORY = "l1_memory"
    L2_REDIS = "l2_redis"
    L3_DATABASE = "l3_database"


class CacheStrategy(Enum):
    """Cache eviction and management strategies."""
    LRU = "lru"  # Least Recently Used
    LFU = "lfu"  # Least Frequently Used
    TTL = "ttl"  # Time To Live
    ADAPTIVE = "adaptive"  # Adaptive based on access patterns


@dataclass
class CacheEntry:
    """Cache entry with comprehensive metadata."""
    key: str
    value: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    ttl: Optional[int] = None
    size_bytes: int = 0
    cache_level: CacheLevel = CacheLevel.L1_MEMORY
    tags: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    
    def is_expired(self) -> bool:
        """Check if cache entry has expired."""
        if self.ttl is None:
            return False
        return datetime.now() > self.created_at + timedelta(seconds=self.ttl)
    
    def update_access(self):
        """Update access metadata."""
        self.last_accessed = datetime.now()
        self.access_count += 1
    
    def get_age_seconds(self) -> float:
        """Get age of cache entry in seconds."""
        return (datetime.now() - self.created_at).total_seconds()


@dataclass
class CacheMetrics:
    """Comprehensive cache performance metrics."""
    l1_hits: int = 0
    l1_misses: int = 0
    l2_hits: int = 0
    l2_misses: int = 0
    l3_hits: int = 0
    l3_misses: int = 0
    evictions: int = 0
    invalidations: int = 0
    warming_operations: int = 0
    prefetch_operations: int = 0
    total_requests: int = 0
    total_response_time: float = 0.0
    
    def get_hit_rate(self, level: Optional[CacheLevel] = None) -> float:
        """Calculate hit rate for specific level or overall."""
        if level == CacheLevel.L1_MEMORY:
            total = self.l1_hits + self.l1_misses
            return (self.l1_hits / total * 100) if total > 0 else 0
        elif level == CacheLevel.L2_REDIS:
            total = self.l2_hits + self.l2_misses
            return (self.l2_hits / total * 100) if total > 0 else 0
        elif level == CacheLevel.L3_DATABASE:
            total = self.l3_hits + self.l3_misses
            return (self.l3_hits / total * 100) if total > 0 else 0
        else:
            # Overall hit rate
            total_hits = self.l1_hits + self.l2_hits + self.l3_hits
            total_requests = total_hits + self.l1_misses + self.l2_misses + self.l3_misses
            return (total_hits / total_requests * 100) if total_requests > 0 else 0
    
    def get_avg_response_time(self) -> float:
        """Calculate average response time."""
        return self.total_response_time / self.total_requests if self.total_requests > 0 else 0


class MultiLevelCache:
    """
    Advanced multi-level caching system with intelligent management.
    
    Provides three levels of caching:
    - L1: In-memory cache for ultra-fast access
    - L2: Redis cache for distributed caching
    - L3: Database cache for persistent storage
    """
    
    def __init__(
        self,
        l1_max_size: int = 1000,
        l1_ttl: int = 300,  # 5 minutes
        l2_ttl: int = 3600,  # 1 hour
        l3_ttl: int = 86400,  # 24 hours
        strategy: CacheStrategy = CacheStrategy.ADAPTIVE
    ):
        # L1 Memory Cache
        self.l1_cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self.l1_max_size = l1_max_size
        self.l1_ttl = l1_ttl
        self.l1_lock = threading.RLock()
        
        # L2 Redis Cache
        self.l2_ttl = l2_ttl
        self.l2_prefix = "datagenius:cache:l2:"
        
        # L3 Database Cache
        self.l3_ttl = l3_ttl
        self.l3_table = "cache_entries"
        
        # Configuration
        self.strategy = strategy
        self.metrics = CacheMetrics()
        
        # Cache warming and prefetching
        self.warming_enabled = True
        self.prefetch_enabled = True
        self.warm_patterns: List[str] = []
        self.prefetch_rules: Dict[str, Callable] = {}
        
        # Invalidation tracking
        self.tag_dependencies: Dict[str, List[str]] = {}
        self.key_dependencies: Dict[str, List[str]] = {}
        
        logger.info("Multi-level cache initialized")
    
    async def get(self, key: str, default: Any = None) -> Any:
        """
        Get value from cache, checking all levels in order.
        
        Args:
            key: Cache key
            default: Default value if not found
            
        Returns:
            Cached value or default
        """
        start_time = time.time()
        
        try:
            # L1 Memory Cache
            value = await self._get_l1(key)
            if value is not None:
                self.metrics.l1_hits += 1
                return value
            self.metrics.l1_misses += 1
            
            # L2 Redis Cache
            value = await self._get_l2(key)
            if value is not None:
                self.metrics.l2_hits += 1
                # Promote to L1
                await self._set_l1(key, value, self.l1_ttl)
                return value
            self.metrics.l2_misses += 1
            
            # L3 Database Cache
            value = await self._get_l3(key)
            if value is not None:
                self.metrics.l3_hits += 1
                # Promote to L2 and L1
                await self._set_l2(key, value, self.l2_ttl)
                await self._set_l1(key, value, self.l1_ttl)
                return value
            self.metrics.l3_misses += 1
            
            return default
            
        finally:
            response_time = time.time() - start_time
            self.metrics.total_requests += 1
            self.metrics.total_response_time += response_time
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        tags: Optional[List[str]] = None,
        dependencies: Optional[List[str]] = None,
        level: Optional[CacheLevel] = None
    ) -> bool:
        """
        Set value in cache at specified or optimal level.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds
            tags: Tags for invalidation
            dependencies: Key dependencies
            level: Specific cache level or auto-select
            
        Returns:
            True if successful
        """
        try:
            # Determine optimal cache level if not specified
            if level is None:
                level = self._determine_optimal_level(value)
            
            # Set in specified level and potentially promote/demote
            success = False
            
            if level == CacheLevel.L1_MEMORY:
                success = await self._set_l1(key, value, ttl or self.l1_ttl, tags, dependencies)
            elif level == CacheLevel.L2_REDIS:
                success = await self._set_l2(key, value, ttl or self.l2_ttl, tags, dependencies)
                # Also set in L1 for faster access
                await self._set_l1(key, value, ttl or self.l1_ttl, tags, dependencies)
            elif level == CacheLevel.L3_DATABASE:
                success = await self._set_l3(key, value, ttl or self.l3_ttl, tags, dependencies)
                # Also set in L2 and L1
                await self._set_l2(key, value, ttl or self.l2_ttl, tags, dependencies)
                await self._set_l1(key, value, ttl or self.l1_ttl, tags, dependencies)
            
            # Track dependencies
            if tags:
                for tag in tags:
                    if tag not in self.tag_dependencies:
                        self.tag_dependencies[tag] = []
                    if key not in self.tag_dependencies[tag]:
                        self.tag_dependencies[tag].append(key)
            
            if dependencies:
                for dep in dependencies:
                    if dep not in self.key_dependencies:
                        self.key_dependencies[dep] = []
                    if key not in self.key_dependencies[dep]:
                        self.key_dependencies[dep].append(key)
            
            return success
            
        except Exception as e:
            logger.error(f"Cache set failed for key {key}: {e}")
            return False
    
    async def invalidate(self, key: str) -> bool:
        """Invalidate key from all cache levels."""
        try:
            # Remove from all levels
            await self._delete_l1(key)
            await self._delete_l2(key)
            await self._delete_l3(key)
            
            # Invalidate dependent keys
            if key in self.key_dependencies:
                for dependent_key in self.key_dependencies[key]:
                    await self.invalidate(dependent_key)
                del self.key_dependencies[key]
            
            self.metrics.invalidations += 1
            return True
            
        except Exception as e:
            logger.error(f"Cache invalidation failed for key {key}: {e}")
            return False
    
    async def invalidate_by_tag(self, tag: str) -> int:
        """Invalidate all keys with specified tag."""
        invalidated_count = 0
        
        if tag in self.tag_dependencies:
            keys_to_invalidate = self.tag_dependencies[tag].copy()
            
            for key in keys_to_invalidate:
                if await self.invalidate(key):
                    invalidated_count += 1
            
            del self.tag_dependencies[tag]
        
        return invalidated_count
    
    async def invalidate_pattern(self, pattern: str) -> int:
        """Invalidate all keys matching pattern."""
        invalidated_count = 0
        
        # L1 Memory Cache
        with self.l1_lock:
            keys_to_remove = [k for k in self.l1_cache.keys() if self._matches_pattern(k, pattern)]
            for key in keys_to_remove:
                del self.l1_cache[key]
                invalidated_count += 1
        
        # L2 Redis Cache
        if redis_client:
            try:
                redis_pattern = f"{self.l2_prefix}{pattern}"
                keys = redis_client.keys(redis_pattern)
                if keys:
                    redis_client.delete(*keys)
                    invalidated_count += len(keys)
            except Exception as e:
                logger.warning(f"Redis pattern invalidation failed: {e}")
        
        # L3 Database Cache would require a database query
        # Implementation depends on specific database schema
        
        return invalidated_count
    
    def _determine_optimal_level(self, value: Any) -> CacheLevel:
        """Determine optimal cache level based on value characteristics."""
        try:
            # Estimate size
            size = len(pickle.dumps(value))
            
            # Small values go to L1
            if size < 1024:  # 1KB
                return CacheLevel.L1_MEMORY
            # Medium values go to L2
            elif size < 1024 * 1024:  # 1MB
                return CacheLevel.L2_REDIS
            # Large values go to L3
            else:
                return CacheLevel.L3_DATABASE
                
        except Exception:
            # Default to L2 if size estimation fails
            return CacheLevel.L2_REDIS
    
    async def _get_l1(self, key: str) -> Any:
        """Get value from L1 memory cache."""
        with self.l1_lock:
            if key in self.l1_cache:
                entry = self.l1_cache[key]
                
                if entry.is_expired():
                    del self.l1_cache[key]
                    return None
                
                entry.update_access()
                # Move to end for LRU
                self.l1_cache.move_to_end(key)
                return entry.value
        
        return None
    
    async def _set_l1(
        self,
        key: str,
        value: Any,
        ttl: int,
        tags: Optional[List[str]] = None,
        dependencies: Optional[List[str]] = None
    ) -> bool:
        """Set value in L1 memory cache."""
        try:
            with self.l1_lock:
                # Evict if cache is full
                while len(self.l1_cache) >= self.l1_max_size:
                    self._evict_l1_entry()
                
                entry = CacheEntry(
                    key=key,
                    value=value,
                    created_at=datetime.now(),
                    last_accessed=datetime.now(),
                    ttl=ttl,
                    cache_level=CacheLevel.L1_MEMORY,
                    tags=tags or [],
                    dependencies=dependencies or []
                )
                
                self.l1_cache[key] = entry
                return True
                
        except Exception as e:
            logger.error(f"L1 cache set failed for key {key}: {e}")
            return False
    
    async def _delete_l1(self, key: str) -> bool:
        """Delete key from L1 memory cache."""
        with self.l1_lock:
            if key in self.l1_cache:
                del self.l1_cache[key]
                return True
        return False
    
    def _evict_l1_entry(self):
        """Evict entry from L1 cache based on strategy."""
        if not self.l1_cache:
            return
        
        if self.strategy == CacheStrategy.LRU:
            # Remove least recently used (first item in OrderedDict)
            self.l1_cache.popitem(last=False)
        elif self.strategy == CacheStrategy.LFU:
            # Remove least frequently used
            min_key = min(self.l1_cache.keys(), 
                         key=lambda k: self.l1_cache[k].access_count)
            del self.l1_cache[min_key]
        else:
            # Default to LRU
            self.l1_cache.popitem(last=False)
        
        self.metrics.evictions += 1
    
    async def _get_l2(self, key: str) -> Any:
        """Get value from L2 Redis cache."""
        if not redis_client:
            return None
        
        try:
            redis_key = f"{self.l2_prefix}{key}"
            cached_data = redis_client.get(redis_key)
            
            if cached_data:
                return pickle.loads(cached_data)
            
        except Exception as e:
            logger.warning(f"L2 cache get failed for key {key}: {e}")
        
        return None
    
    async def _set_l2(
        self,
        key: str,
        value: Any,
        ttl: int,
        tags: Optional[List[str]] = None,
        dependencies: Optional[List[str]] = None
    ) -> bool:
        """Set value in L2 Redis cache."""
        if not redis_client:
            return False
        
        try:
            redis_key = f"{self.l2_prefix}{key}"
            serialized_value = pickle.dumps(value)
            redis_client.setex(redis_key, ttl, serialized_value)
            return True
            
        except Exception as e:
            logger.warning(f"L2 cache set failed for key {key}: {e}")
            return False
    
    async def _delete_l2(self, key: str) -> bool:
        """Delete key from L2 Redis cache."""
        if not redis_client:
            return False
        
        try:
            redis_key = f"{self.l2_prefix}{key}"
            return redis_client.delete(redis_key) > 0
            
        except Exception as e:
            logger.warning(f"L2 cache delete failed for key {key}: {e}")
            return False
    
    async def _get_l3(self, key: str) -> Any:
        """Get value from L3 database cache."""
        # Implementation would depend on database schema
        # For now, return None (not implemented)
        return None
    
    async def _set_l3(
        self,
        key: str,
        value: Any,
        ttl: int,
        tags: Optional[List[str]] = None,
        dependencies: Optional[List[str]] = None
    ) -> bool:
        """Set value in L3 database cache."""
        # Implementation would depend on database schema
        # For now, return False (not implemented)
        return False
    
    async def _delete_l3(self, key: str) -> bool:
        """Delete key from L3 database cache."""
        # Implementation would depend on database schema
        # For now, return False (not implemented)
        return False
    
    def _matches_pattern(self, key: str, pattern: str) -> bool:
        """Check if key matches pattern (simple wildcard support)."""
        if '*' not in pattern:
            return key == pattern
        
        # Simple wildcard matching
        pattern_parts = pattern.split('*')
        if len(pattern_parts) == 2:
            prefix, suffix = pattern_parts
            return key.startswith(prefix) and key.endswith(suffix)
        
        return False
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get comprehensive cache metrics."""
        return {
            'hit_rates': {
                'l1': self.metrics.get_hit_rate(CacheLevel.L1_MEMORY),
                'l2': self.metrics.get_hit_rate(CacheLevel.L2_REDIS),
                'l3': self.metrics.get_hit_rate(CacheLevel.L3_DATABASE),
                'overall': self.metrics.get_hit_rate()
            },
            'requests': {
                'l1_hits': self.metrics.l1_hits,
                'l1_misses': self.metrics.l1_misses,
                'l2_hits': self.metrics.l2_hits,
                'l2_misses': self.metrics.l2_misses,
                'l3_hits': self.metrics.l3_hits,
                'l3_misses': self.metrics.l3_misses,
                'total': self.metrics.total_requests
            },
            'operations': {
                'evictions': self.metrics.evictions,
                'invalidations': self.metrics.invalidations,
                'warming_operations': self.metrics.warming_operations,
                'prefetch_operations': self.metrics.prefetch_operations
            },
            'performance': {
                'avg_response_time': self.metrics.get_avg_response_time(),
                'total_response_time': self.metrics.total_response_time
            },
            'cache_sizes': {
                'l1_entries': len(self.l1_cache),
                'l1_max_size': self.l1_max_size
            }
        }


class CacheWarmer:
    """
    Cache warming system for proactive cache population.

    Provides intelligent cache warming based on access patterns,
    scheduled warming, and predictive prefetching.
    """

    def __init__(self, cache: MultiLevelCache):
        self.cache = cache
        self.warming_rules: Dict[str, Callable] = {}
        self.warming_schedule: Dict[str, Dict[str, Any]] = {}
        self.access_patterns: Dict[str, List[datetime]] = {}
        self.warming_enabled = True

    def add_warming_rule(self, name: str, rule_func: Callable, schedule: Optional[Dict[str, Any]] = None):
        """Add a cache warming rule."""
        self.warming_rules[name] = rule_func
        if schedule:
            self.warming_schedule[name] = schedule

    async def warm_cache(self, patterns: Optional[List[str]] = None) -> Dict[str, Any]:
        """Warm cache based on patterns or rules."""
        if not self.warming_enabled:
            return {'status': 'disabled'}

        results = {
            'warmed_keys': [],
            'failed_keys': [],
            'total_time': 0.0
        }

        start_time = time.time()

        try:
            # Warm based on patterns
            if patterns:
                for pattern in patterns:
                    await self._warm_pattern(pattern, results)

            # Execute warming rules
            for rule_name, rule_func in self.warming_rules.items():
                try:
                    keys_to_warm = await rule_func()
                    for key, value in keys_to_warm.items():
                        success = await self.cache.set(key, value, level=CacheLevel.L2_REDIS)
                        if success:
                            results['warmed_keys'].append(key)
                        else:
                            results['failed_keys'].append(key)
                except Exception as e:
                    logger.error(f"Cache warming rule {rule_name} failed: {e}")
                    results['failed_keys'].append(f"rule:{rule_name}")

            results['total_time'] = time.time() - start_time
            self.cache.metrics.warming_operations += 1

            logger.info(f"Cache warming completed: {len(results['warmed_keys'])} keys warmed")

        except Exception as e:
            logger.error(f"Cache warming failed: {e}")
            results['error'] = str(e)

        return results

    async def _warm_pattern(self, pattern: str, results: Dict[str, Any]):
        """Warm cache for a specific pattern."""
        # This would typically query the database or other data sources
        # to populate cache entries matching the pattern
        pass

    def track_access(self, key: str):
        """Track access patterns for predictive warming."""
        if key not in self.access_patterns:
            self.access_patterns[key] = []

        self.access_patterns[key].append(datetime.now())

        # Keep only recent accesses (last 24 hours)
        cutoff = datetime.now() - timedelta(hours=24)
        self.access_patterns[key] = [
            access_time for access_time in self.access_patterns[key]
            if access_time > cutoff
        ]

    def get_popular_keys(self, limit: int = 100) -> List[str]:
        """Get most frequently accessed keys for warming."""
        key_scores = {}

        for key, accesses in self.access_patterns.items():
            # Score based on frequency and recency
            recent_accesses = len([a for a in accesses if a > datetime.now() - timedelta(hours=1)])
            total_accesses = len(accesses)
            key_scores[key] = recent_accesses * 2 + total_accesses

        # Sort by score and return top keys
        sorted_keys = sorted(key_scores.items(), key=lambda x: x[1], reverse=True)
        return [key for key, score in sorted_keys[:limit]]


class CacheInvalidationManager:
    """
    Advanced cache invalidation management system.

    Provides intelligent invalidation strategies, dependency tracking,
    and event-driven invalidation.
    """

    def __init__(self, cache: MultiLevelCache):
        self.cache = cache
        self.invalidation_rules: Dict[str, Callable] = {}
        self.event_handlers: Dict[str, List[Callable]] = {}
        self.dependency_graph: Dict[str, List[str]] = {}

    def add_invalidation_rule(self, event_type: str, rule_func: Callable):
        """Add an invalidation rule for specific events."""
        self.invalidation_rules[event_type] = rule_func

    def add_event_handler(self, event_type: str, handler: Callable):
        """Add event handler for cache invalidation."""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)

    async def handle_event(self, event_type: str, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle invalidation event."""
        results = {
            'invalidated_keys': [],
            'invalidated_tags': [],
            'total_invalidations': 0
        }

        try:
            # Execute invalidation rule
            if event_type in self.invalidation_rules:
                rule_func = self.invalidation_rules[event_type]
                invalidation_targets = await rule_func(event_data)

                # Process invalidation targets
                for target in invalidation_targets:
                    if target.startswith('tag:'):
                        tag = target[4:]  # Remove 'tag:' prefix
                        count = await self.cache.invalidate_by_tag(tag)
                        results['invalidated_tags'].append(tag)
                        results['total_invalidations'] += count
                    elif target.startswith('pattern:'):
                        pattern = target[8:]  # Remove 'pattern:' prefix
                        count = await self.cache.invalidate_pattern(pattern)
                        results['total_invalidations'] += count
                    else:
                        # Direct key invalidation
                        success = await self.cache.invalidate(target)
                        if success:
                            results['invalidated_keys'].append(target)
                            results['total_invalidations'] += 1

            # Execute event handlers
            if event_type in self.event_handlers:
                for handler in self.event_handlers[event_type]:
                    try:
                        await handler(event_data, results)
                    except Exception as e:
                        logger.error(f"Event handler failed for {event_type}: {e}")

            logger.info(f"Cache invalidation completed for {event_type}: "
                       f"{results['total_invalidations']} entries invalidated")

        except Exception as e:
            logger.error(f"Cache invalidation failed for {event_type}: {e}")
            results['error'] = str(e)

        return results

    def add_dependency(self, parent_key: str, dependent_key: str):
        """Add dependency relationship between cache keys."""
        if parent_key not in self.dependency_graph:
            self.dependency_graph[parent_key] = []

        if dependent_key not in self.dependency_graph[parent_key]:
            self.dependency_graph[parent_key].append(dependent_key)

    async def invalidate_dependencies(self, key: str) -> int:
        """Invalidate all dependent keys."""
        invalidated_count = 0

        if key in self.dependency_graph:
            for dependent_key in self.dependency_graph[key]:
                success = await self.cache.invalidate(dependent_key)
                if success:
                    invalidated_count += 1

                # Recursively invalidate dependencies
                invalidated_count += await self.invalidate_dependencies(dependent_key)

        return invalidated_count


# Enhanced cache decorator for automatic caching
def cached(
    ttl: int = 3600,
    level: Optional[CacheLevel] = None,
    tags: Optional[List[str]] = None,
    key_func: Optional[Callable] = None
):
    """Decorator for automatic function result caching."""
    def decorator(func: Callable):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # Default key generation
                key_parts = [func.__name__]
                key_parts.extend(str(arg) for arg in args)
                key_parts.extend(f"{k}={v}" for k, v in sorted(kwargs.items()))
                cache_key = hashlib.md5(":".join(key_parts).encode()).hexdigest()

            # Try to get from cache
            cached_result = await multi_cache.get(cache_key)
            if cached_result is not None:
                return cached_result

            # Execute function and cache result
            result = await func(*args, **kwargs)
            await multi_cache.set(cache_key, result, ttl=ttl, level=level, tags=tags)

            return result

        return wrapper
    return decorator


# Global instances
multi_cache = MultiLevelCache()
cache_warmer = CacheWarmer(multi_cache)
invalidation_manager = CacheInvalidationManager(multi_cache)


async def get_comprehensive_cache_metrics() -> Dict[str, Any]:
    """Get comprehensive caching system metrics."""
    return {
        'multi_level_cache': multi_cache.get_metrics(),
        'warming_stats': {
            'popular_keys_count': len(cache_warmer.get_popular_keys()),
            'warming_rules': len(cache_warmer.warming_rules),
            'access_patterns_tracked': len(cache_warmer.access_patterns)
        },
        'invalidation_stats': {
            'invalidation_rules': len(invalidation_manager.invalidation_rules),
            'event_handlers': sum(len(handlers) for handlers in invalidation_manager.event_handlers.values()),
            'dependency_relationships': len(invalidation_manager.dependency_graph)
        }
    }
